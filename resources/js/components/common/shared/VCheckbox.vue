<script setup>
import { v4 as uuid } from 'uuid';

const props = defineProps({
  modelValue: [<PERSON><PERSON><PERSON>, String, Number],
  options: {
    type: Object,
    required: true,
  },
  label: String,
  error: String,
  name: String,
  id: {
    type: String,
    default() {
      return `checkbox-${uuid()}`;
    },
  },
});

defineEmits(['update:modelValue']);

const newOptions = computed(() => {
  const options = [];
  if (props.options) {
    if (Array.isArray(props.options)) {
      return props.options;
    } else {
      Object.keys(props.options).forEach(key => {
        options.push({ label: props.options[key], value: key });
      });
    }
  }

  if (props.hasSearch && searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    return options.filter(
      option => option.label.toLowerCase().includes(query) || option.value.toLowerCase().includes(query),
    );
  }

  return options;
});
</script>

<template>
  <div>
    <label v-if="label" class="mb-2 block text-sm font-medium text-gray-700" :for="id">{{ label }}</label>
    <div class="flex items-center">
      <div v-for="option in newOptions" :key="option.value" class="mb-2 mr-4">
        <input
          :id="`${id}-${option.value}`"
          type="checkbox"
          :name="name"
          :value="option.value"
          :checked="modelValue == option.value"
          class="mr-2 form-checkbox rounded"
          @change="$emit('update:modelValue', option.value)"
        />
        <label :for="`${id}-${option.value}`">{{ option.label }}</label>
      </div>
    </div>
    <p v-if="errorerror" class="text-theme-sm text-error-500">{{ error }}</p>
  </div>
</template>

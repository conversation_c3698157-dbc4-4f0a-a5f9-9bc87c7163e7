<?php

namespace Src\Traits;

use Src\Domain\Admin\Services\EditorService;

trait EditorImageTrait
{
    /**
     * Process editor content to move temporary images to permanent storage
     *
     * @param string $content
     * @return string
     */
    protected function processEditorImages(string $content): string
    {
        $editorService = new EditorService();
        return $editorService->moveTemporaryImagesToPermanent($content);
    }

    /**
     * Process multiple editor content fields
     *
     * @param array $fields Array of field names that contain editor content
     * @param array $data Data array containing the content
     * @return array Updated data array with processed content
     */
    protected function processMultipleEditorFields(array $fields, array $data): array
    {
        foreach ($fields as $field) {
            if (isset($data[$field]) && is_string($data[$field])) {
                $data[$field] = $this->processEditorImages($data[$field]);
            }
        }
        
        return $data;
    }
}
